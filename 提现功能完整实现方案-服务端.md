# 提现功能完整实现方案

## 🎯 功能概述

基于您的需求，实现类似微信钱包的提现功能，支持提现到微信和银行卡两种方式。

## 📊 数据库表结构设计

### 1. 核心表结构

#### 微信支付账户表
```sql
CREATE TABLE wechat_account (
  id int(11) NOT NULL AUTO_INCREMENT,
  user_id int(11) NOT NULL,
  openid varchar(100) NOT NULL,
  nickname varchar(100) DEFAULT NULL,
  real_name varchar(50) DEFAULT NULL,
  is_verified tinyint(1) DEFAULT '0',
  is_default tinyint(1) DEFAULT '1',
  status tinyint(1) DEFAULT '1',
  created_at datetime DEFAULT CURRENT_TIMESTAMP,
  updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (id),
  UNIQUE KEY user_openid (user_id, openid)
);
```

#### 银行卡表
```sql
CREATE TABLE bank_card (
  id int(11) NOT NULL AUTO_INCREMENT,
  user_id int(11) NOT NULL,
  bank_name varchar(50) NOT NULL,
  bank_code varchar(20) DEFAULT NULL,
  card_number varchar(50) NOT NULL,
  card_number_mask varchar(50) NOT NULL,
  card_holder varchar(50) NOT NULL,
  card_type tinyint(1) DEFAULT '1',
  phone varchar(20) DEFAULT NULL,
  is_default tinyint(1) DEFAULT '0',
  is_verified tinyint(1) DEFAULT '0',
  status tinyint(1) DEFAULT '1',
  created_at datetime DEFAULT CURRENT_TIMESTAMP,
  updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (id)
);
```

#### 提现申请表
```sql
CREATE TABLE withdraw (
  id int(11) NOT NULL AUTO_INCREMENT,
  user_id int(11) NOT NULL,
  withdraw_no varchar(50) NOT NULL,
  amount decimal(10,2) NOT NULL,
  fee decimal(10,2) DEFAULT '0.00',
  actual_amount decimal(10,2) NOT NULL,
  withdraw_type varchar(20) NOT NULL,
  account_id int(11) NOT NULL,
  account_info text DEFAULT NULL,
  status tinyint(1) DEFAULT '0',
  apply_time datetime DEFAULT CURRENT_TIMESTAMP,
  audit_time datetime DEFAULT NULL,
  complete_time datetime DEFAULT NULL,
  transaction_id varchar(100) DEFAULT NULL,
  failure_reason varchar(255) DEFAULT NULL,
  remark varchar(255) DEFAULT NULL,
  created_at datetime DEFAULT CURRENT_TIMESTAMP,
  updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (id),
  UNIQUE KEY withdraw_no (withdraw_no)
);
```

### 2. 配置表

#### 提现配置表
```sql
CREATE TABLE withdraw_config (
  id int(11) NOT NULL AUTO_INCREMENT,
  config_key varchar(50) NOT NULL,
  config_value text NOT NULL,
  config_type varchar(20) DEFAULT 'string',
  description varchar(255) DEFAULT NULL,
  status tinyint(1) DEFAULT '1',
  created_at datetime DEFAULT CURRENT_TIMESTAMP,
  updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (id),
  UNIQUE KEY config_key (config_key)
);
```

#### 银行信息表
```sql
CREATE TABLE bank_info (
  id int(11) NOT NULL AUTO_INCREMENT,
  bank_code varchar(20) NOT NULL,
  bank_name varchar(50) NOT NULL,
  bank_logo varchar(255) DEFAULT NULL,
  is_support tinyint(1) DEFAULT '1',
  min_amount decimal(10,2) DEFAULT '1.00',
  max_amount decimal(10,2) DEFAULT '50000.00',
  fee_rate decimal(5,4) DEFAULT '0.0000',
  sort_order int(11) DEFAULT '0',
  status tinyint(1) DEFAULT '1',
  created_at datetime DEFAULT CURRENT_TIMESTAMP,
  updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (id),
  UNIQUE KEY bank_code (bank_code)
);
```

## 🔧 后端API接口设计

### 1. 提现配置接口
```javascript
// GET /api/v1/client/withdraw/config
router.get('/config', async (req, res) => {
  try {
    const configs = await WithdrawConfig.findAll();
    const configMap = {};
    configs.forEach(config => {
      configMap[config.config_key] = config.config_value;
    });
    
    res.json({
      success: true,
      data: {
        min_amount: parseFloat(configMap.min_withdraw_amount || 10),
        max_amount: parseFloat(configMap.max_withdraw_amount || 50000),
        daily_limit: parseFloat(configMap.daily_withdraw_limit || 100000),
        wechat_fee_rate: parseFloat(configMap.wechat_fee_rate || 0.006),
        bank_fee_rate: parseFloat(configMap.bank_fee_rate || 0.001),
        wechat_min_fee: parseFloat(configMap.wechat_min_fee || 0.1),
        bank_min_fee: parseFloat(configMap.bank_min_fee || 2)
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取配置失败'
    });
  }
});
```

### 2. 提现方式接口
```javascript
// GET /api/v1/client/withdraw/methods
router.get('/methods', async (req, res) => {
  try {
    const userId = req.user.id;
    
    // 获取微信账户
    const wechatAccounts = await WechatAccount.findAll({
      where: { user_id: userId, status: 1 }
    });
    
    // 获取银行卡
    const bankCards = await BankCard.findAll({
      where: { user_id: userId, status: 1 }
    });
    
    res.json({
      success: true,
      data: {
        wechat_accounts: wechatAccounts,
        bank_cards: bankCards
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取提现方式失败'
    });
  }
});
```

### 3. 手续费计算接口
```javascript
// POST /api/v1/client/withdraw/calculate-fee
router.post('/calculate-fee', async (req, res) => {
  try {
    const { amount, withdraw_type } = req.body;
    
    if (!amount || amount <= 0) {
      return res.status(400).json({
        success: false,
        message: '金额无效'
      });
    }
    
    // 获取费率配置
    const configs = await WithdrawConfig.findAll();
    const configMap = {};
    configs.forEach(config => {
      configMap[config.config_key] = config.config_value;
    });
    
    let fee = 0;
    let feeRate = 0;
    
    if (withdraw_type === 'wechat') {
      feeRate = parseFloat(configMap.wechat_fee_rate || 0.006);
      const minFee = parseFloat(configMap.wechat_min_fee || 0.1);
      fee = Math.max(amount * feeRate, minFee);
    } else if (withdraw_type === 'bank_card') {
      feeRate = parseFloat(configMap.bank_fee_rate || 0.001);
      const minFee = parseFloat(configMap.bank_min_fee || 2);
      fee = Math.max(amount * feeRate, minFee);
    }
    
    const actualAmount = amount - fee;
    
    res.json({
      success: true,
      data: {
        amount: parseFloat(amount.toFixed(2)),
        fee: parseFloat(fee.toFixed(2)),
        actual_amount: parseFloat(actualAmount.toFixed(2)),
        fee_rate: feeRate
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '计算手续费失败'
    });
  }
});
```

### 4. 提现申请接口
```javascript
// POST /api/v1/client/withdraw/apply
router.post('/apply', async (req, res) => {
  const transaction = await sequelize.transaction();
  
  try {
    const userId = req.user.id;
    const { amount, withdraw_type, account_id, remark } = req.body;
    
    // 验证参数
    if (!amount || amount <= 0 || !withdraw_type || !account_id) {
      return res.status(400).json({
        success: false,
        message: '参数错误'
      });
    }
    
    // 检查用户余额
    const user = await User.findByPk(userId);
    if (user.balance < amount) {
      return res.status(400).json({
        success: false,
        message: '余额不足'
      });
    }
    
    // 计算手续费
    const feeResult = await calculateWithdrawFee(amount, withdraw_type);
    const fee = feeResult.fee;
    const actualAmount = amount - fee;
    
    // 生成提现单号
    const withdrawNo = generateWithdrawNo();
    
    // 获取账户信息
    let accountInfo = {};
    if (withdraw_type === 'wechat') {
      const wechatAccount = await WechatAccount.findByPk(account_id);
      accountInfo = {
        type: 'wechat',
        openid: wechatAccount.openid,
        nickname: wechatAccount.nickname,
        real_name: wechatAccount.real_name
      };
    } else if (withdraw_type === 'bank_card') {
      const bankCard = await BankCard.findByPk(account_id);
      accountInfo = {
        type: 'bank_card',
        bank_name: bankCard.bank_name,
        card_number_mask: bankCard.card_number_mask,
        card_holder: bankCard.card_holder
      };
    }
    
    // 创建提现记录
    const withdraw = await Withdraw.create({
      user_id: userId,
      withdraw_no: withdrawNo,
      amount: amount,
      fee: fee,
      actual_amount: actualAmount,
      withdraw_type: withdraw_type,
      account_id: account_id,
      account_info: JSON.stringify(accountInfo),
      status: 0, // 待审核
      remark: remark
    }, { transaction });
    
    // 扣减用户余额
    await User.update(
      { balance: sequelize.literal(`balance - ${amount}`) },
      { where: { id: userId }, transaction }
    );
    
    // 记录钱包交易
    await WalletTransaction.create({
      user_id: userId,
      type: 'withdraw',
      amount: -amount,
      balance_before: user.balance,
      balance_after: user.balance - amount,
      related_type: 'withdraw',
      related_id: withdraw.id,
      title: '申请提现',
      description: `提现到${withdraw_type === 'wechat' ? '微信' : '银行卡'}`,
      transaction_no: withdrawNo
    }, { transaction });
    
    await transaction.commit();
    
    res.json({
      success: true,
      data: {
        withdraw_no: withdrawNo,
        status: 'pending_audit',
        estimated_arrival: getEstimatedArrival()
      },
      message: '提现申请已提交'
    });
    
  } catch (error) {
    await transaction.rollback();
    res.status(500).json({
      success: false,
      message: '提现申请失败'
    });
  }
});
```

### 5. 提现记录接口
```javascript
// GET /api/v1/client/withdraw/records
router.get('/records', async (req, res) => {
  try {
    const userId = req.user.id;
    const { page = 1, limit = 10 } = req.query;
    
    const offset = (page - 1) * limit;
    
    const { count, rows } = await Withdraw.findAndCountAll({
      where: { user_id: userId },
      order: [['created_at', 'DESC']],
      limit: parseInt(limit),
      offset: offset
    });
    
    // 格式化状态文本
    const records = rows.map(record => ({
      ...record.toJSON(),
      status_text: getStatusText(record.status),
      account_info: JSON.parse(record.account_info || '{}')
    }));
    
    res.json({
      success: true,
      data: {
        list: records,
        total: count,
        page: parseInt(page),
        limit: parseInt(limit)
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取提现记录失败'
    });
  }
});
```

## 📱 前端页面实现

### 1. 钱包页面修改

移除下方的收入来源显示，只保留核心功能：

```javascript
// pages/user/wallet/wallet.js
Page({
  data: {
    balance: 0,
    totalIncome: 0,
    todayIncome: 0,
    monthIncome: 0
  },
  
  onLoad() {
    this.loadWalletData();
  },
  
  // 加载钱包数据
  async loadWalletData() {
    try {
      const result = await request.get('/api/v1/client/income/stats');
      if (result.success) {
        this.setData({
          balance: result.data.balance || 0,
          totalIncome: result.data.total_income || 0,
          todayIncome: result.data.today_income || 0,
          monthIncome: result.data.month_income || 0
        });
      }
    } catch (error) {
      console.error('加载钱包数据失败:', error);
    }
  },
  
  // 跳转到提现页面
  goToWithdraw() {
    if (this.data.balance <= 0) {
      wx.showToast({
        title: '余额不足',
        icon: 'none'
      });
      return;
    }
    
    wx.navigateTo({
      url: '/pages/user/withdraw/withdraw'
    });
  },
  
  // 跳转到充值页面
  goToRecharge() {
    wx.showToast({
      title: '充值功能暂未开放',
      icon: 'none'
    });
  },
  
  // 跳转到明细页面
  goToDetails() {
    wx.navigateTo({
      url: '/pages/user/income-details/income-details'
    });
  }
});
```

### 2. 钱包页面WXML
```xml
<view class="wallet-container">
  <!-- 余额卡片 -->
  <view class="balance-card">
    <view class="balance-title">账户余额</view>
    <view class="balance-amount">¥{{balance}}</view>
    <view class="balance-tip">可提现余额</view>
  </view>
  
  <!-- 操作按钮 -->
  <view class="action-buttons">
    <view class="action-item" bindtap="goToRecharge">
      <view class="action-icon">
        <image src="/assets/icons/recharge.png" />
      </view>
      <text>充值</text>
    </view>
    
    <view class="action-item" bindtap="goToWithdraw">
      <view class="action-icon">
        <image src="/assets/icons/withdraw.png" />
      </view>
      <text>提现</text>
    </view>
    
    <view class="action-item" bindtap="goToDetails">
      <view class="action-icon">
        <image src="/assets/icons/details.png" />
      </view>
      <text>明细</text>
    </view>
  </view>
  
  <!-- 收入统计 -->
  <view class="income-stats">
    <view class="stats-item">
      <view class="stats-value">¥{{totalIncome}}</view>
      <view class="stats-label">总收入</view>
    </view>
    <view class="stats-item">
      <view class="stats-value">¥{{todayIncome}}</view>
      <view class="stats-label">今日收入</view>
    </view>
    <view class="stats-item">
      <view class="stats-value">¥{{monthIncome}}</view>
      <view class="stats-label">本月收入</view>
    </view>
  </view>
</view>
```

## 🎨 样式设计

### 钱包页面样式
```css
.wallet-container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.balance-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20rpx;
  padding: 40rpx;
  color: white;
  text-align: center;
  margin-bottom: 30rpx;
}

.balance-amount {
  font-size: 60rpx;
  font-weight: bold;
  margin: 20rpx 0;
}

.action-buttons {
  display: flex;
  justify-content: space-around;
  background: white;
  border-radius: 16rpx;
  padding: 40rpx 20rpx;
  margin-bottom: 30rpx;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #333;
}

.action-icon {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 10rpx;
}

.income-stats {
  display: flex;
  justify-content: space-around;
  background: white;
  border-radius: 16rpx;
  padding: 40rpx 20rpx;
}

.stats-item {
  text-align: center;
}

.stats-value {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.stats-label {
  font-size: 24rpx;
  color: #999;
}
```

## 🎉 实现效果

完成后的提现功能将具备：

1. **简洁的钱包界面**：移除了下方的收入来源显示，保持界面简洁
2. **完整的提现流程**：支持微信和银行卡两种提现方式
3. **实时手续费计算**：根据提现方式和金额自动计算手续费
4. **安全的资金管理**：完整的余额验证和交易记录
5. **状态跟踪**：详细的提现状态和进度显示

这个方案提供了完整的提现功能实现，可以满足您类似微信钱包的提现需求。
