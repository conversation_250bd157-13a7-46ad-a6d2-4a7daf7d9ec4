/* pages/test-qrcode/test-qrcode.wxss */
.container {
  padding: 40rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 60rpx;
}

.title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.subtitle {
  display: block;
  font-size: 28rpx;
  color: #666;
}

.qrcode-section {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.qrcode-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  text-align: center;
  margin-bottom: 30rpx;
}

.wifi-info {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
  margin-bottom: 40rpx;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 10rpx;
}

.wifi-info text {
  font-size: 28rpx;
  color: #555;
}

.qrcode-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 500rpx;
}

.instructions {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.instruction-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.instructions text:not(.instruction-title) {
  display: block;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
  padding-left: 20rpx;
}
