// pages/test-qrcode/test-qrcode.js
// 二维码测试页面

Page({
  /**
   * 页面的初始数据
   */
  data: {
    testWifi: {
      ssid: 'TestWiFi',
      password: '12345678'
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    console.log('二维码测试页面加载');
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {
    console.log('二维码测试页面渲染完成');
  },

  /**
   * 二维码生成完成事件
   */
  onQrCodeGenerated: function(e) {
    console.log('二维码生成完成:', e.detail);
  },

  /**
   * 二维码点击事件
   */
  onQrCodeTap: function(e) {
    console.log('二维码被点击:', e.detail);
  },

  /**
   * 二维码长按事件
   */
  onQrCodeLongPress: function(e) {
    console.log('二维码被长按:', e.detail);
  }
});
