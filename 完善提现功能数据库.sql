-- 完善提现功能数据库表结构
-- 执行时间：2025年1月29日

USE mall;

-- 1. 完善微信支付账户表
CREATE TABLE IF NOT EXISTS `wechat_account` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '微信账户ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `openid` varchar(100) NOT NULL COMMENT '微信openid',
  `nickname` varchar(100) DEFAULT NULL COMMENT '微信昵称',
  `real_name` varchar(50) DEFAULT NULL COMMENT '真实姓名',
  `is_verified` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否实名认证：0否，1是',
  `is_default` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否默认提现方式：0否，1是',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0禁用，1启用',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_openid` (`user_id`, `openid`),
  KEY `user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='微信支付账户表';

-- 2. 完善银行卡表（添加缺失字段）
ALTER TABLE `bank_card` 
ADD COLUMN IF NOT EXISTS `bank_code` varchar(20) DEFAULT NULL COMMENT '银行代码' AFTER `bank_name`,
ADD COLUMN IF NOT EXISTS `card_number_mask` varchar(50) DEFAULT NULL COMMENT '卡号掩码显示' AFTER `card_number`,
ADD COLUMN IF NOT EXISTS `card_type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '卡类型：1储蓄卡，2信用卡' AFTER `card_holder`,
ADD COLUMN IF NOT EXISTS `phone` varchar(20) DEFAULT NULL COMMENT '预留手机号' AFTER `card_type`,
ADD COLUMN IF NOT EXISTS `id_card` varchar(50) DEFAULT NULL COMMENT '身份证号（加密存储）' AFTER `phone`,
ADD COLUMN IF NOT EXISTS `is_verified` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已验证：0否，1是' AFTER `is_default`,
ADD COLUMN IF NOT EXISTS `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0禁用，1启用' AFTER `is_verified`;

-- 3. 完善提现申请表（添加更多字段）
ALTER TABLE `withdraw`
ADD COLUMN IF NOT EXISTS `withdraw_no` varchar(50) DEFAULT NULL COMMENT '提现单号' AFTER `user_id`,
ADD COLUMN IF NOT EXISTS `fee` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '手续费' AFTER `amount`,
ADD COLUMN IF NOT EXISTS `actual_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '实际到账金额' AFTER `fee`,
ADD COLUMN IF NOT EXISTS `withdraw_type` varchar(20) NOT NULL DEFAULT 'bank_card' COMMENT '提现方式：wechat,bank_card' AFTER `actual_amount`,
ADD COLUMN IF NOT EXISTS `account_id` int(11) DEFAULT NULL COMMENT '账户ID（银行卡ID或微信账户ID）' AFTER `withdraw_type`,
ADD COLUMN IF NOT EXISTS `account_info` text DEFAULT NULL COMMENT '账户信息快照（JSON格式）' AFTER `account_id`,
ADD COLUMN IF NOT EXISTS `apply_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '申请时间' AFTER `account_info`,
ADD COLUMN IF NOT EXISTS `audit_user_id` int(11) DEFAULT NULL COMMENT '审核人ID' AFTER `apply_time`,
ADD COLUMN IF NOT EXISTS `audit_time` datetime DEFAULT NULL COMMENT '审核时间' AFTER `audit_user_id`,
ADD COLUMN IF NOT EXISTS `audit_remark` varchar(255) DEFAULT NULL COMMENT '审核备注' AFTER `audit_time`,
ADD COLUMN IF NOT EXISTS `process_time` datetime DEFAULT NULL COMMENT '处理时间' AFTER `audit_remark`,
ADD COLUMN IF NOT EXISTS `complete_time` datetime DEFAULT NULL COMMENT '完成时间' AFTER `process_time`,
ADD COLUMN IF NOT EXISTS `transaction_id` varchar(100) DEFAULT NULL COMMENT '微信支付交易ID' AFTER `complete_time`,
ADD COLUMN IF NOT EXISTS `bank_transaction_id` varchar(100) DEFAULT NULL COMMENT '银行交易流水号' AFTER `transaction_id`,
ADD COLUMN IF NOT EXISTS `failure_reason` varchar(255) DEFAULT NULL COMMENT '失败原因' AFTER `bank_transaction_id`;

-- 更新提现状态字段注释
ALTER TABLE `withdraw` MODIFY COLUMN `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态：0待审核，1审核通过，2审核拒绝，3处理中，4已完成，5已取消';

-- 4. 创建提现配置表
CREATE TABLE IF NOT EXISTS `withdraw_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `config_key` varchar(50) NOT NULL COMMENT '配置键',
  `config_value` text NOT NULL COMMENT '配置值',
  `config_type` varchar(20) NOT NULL DEFAULT 'string' COMMENT '配置类型：string,number,boolean,json',
  `description` varchar(255) DEFAULT NULL COMMENT '配置描述',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0禁用，1启用',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `config_key` (`config_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='提现配置表';

-- 5. 创建银行信息表
CREATE TABLE IF NOT EXISTS `bank_info` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '银行ID',
  `bank_code` varchar(20) NOT NULL COMMENT '银行代码',
  `bank_name` varchar(50) NOT NULL COMMENT '银行名称',
  `bank_logo` varchar(255) DEFAULT NULL COMMENT '银行logo',
  `is_support` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否支持：0否，1是',
  `min_amount` decimal(10,2) NOT NULL DEFAULT '1.00' COMMENT '最小提现金额',
  `max_amount` decimal(10,2) NOT NULL DEFAULT '50000.00' COMMENT '最大提现金额',
  `fee_rate` decimal(5,4) NOT NULL DEFAULT '0.0000' COMMENT '手续费率',
  `sort_order` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0禁用，1启用',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `bank_code` (`bank_code`),
  KEY `status` (`status`),
  KEY `sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='银行信息表';

-- 6. 插入提现配置数据
INSERT INTO `withdraw_config` (`config_key`, `config_value`, `config_type`, `description`) VALUES
('min_withdraw_amount', '10', 'number', '最小提现金额（元）'),
('max_withdraw_amount', '50000', 'number', '最大提现金额（元）'),
('daily_withdraw_limit', '100000', 'number', '每日提现限额（元）'),
('wechat_fee_rate', '0.006', 'number', '微信提现手续费率（0.6%）'),
('bank_fee_rate', '0.001', 'number', '银行卡提现手续费率（0.1%）'),
('wechat_min_fee', '0.1', 'number', '微信提现最小手续费（元）'),
('bank_min_fee', '2', 'number', '银行卡提现最小手续费（元）'),
('auto_audit_enabled', 'true', 'boolean', '是否启用自动审核'),
('auto_audit_limit', '1000', 'number', '自动审核金额限制（元）'),
('working_hours', '{"start": "09:00", "end": "18:00"}', 'json', '工作时间配置'),
('weekend_process', 'false', 'boolean', '是否周末处理提现'),
('notification_enabled', 'true', 'boolean', '是否启用提现通知'),
('wechat_mch_id', 'your_mch_id', 'string', '微信支付商户号'),
('wechat_api_key', 'your_api_key', 'string', '微信支付API密钥'),
('wechat_cert_path', '/path/to/cert.pem', 'string', '微信支付证书路径'),
('wechat_auto_withdraw_enabled', 'false', 'boolean', '微信自动提现是否启用'),
('wechat_auto_withdraw_type', '2', 'number', '微信自动提现类型：1实时，2日终'),
('wechat_retain_amount', '100000', 'number', '微信账户留存金额（分）')
ON DUPLICATE KEY UPDATE 
  `config_value` = VALUES(`config_value`),
  `description` = VALUES(`description`),
  `updated_at` = CURRENT_TIMESTAMP;

-- 7. 插入银行信息数据
INSERT INTO `bank_info` (`bank_code`, `bank_name`, `min_amount`, `max_amount`, `fee_rate`, `sort_order`) VALUES
('ICBC', '中国工商银行', 10.00, 50000.00, 0.001, 1),
('ABC', '中国农业银行', 10.00, 50000.00, 0.001, 2),
('BOC', '中国银行', 10.00, 50000.00, 0.001, 3),
('CCB', '中国建设银行', 10.00, 50000.00, 0.001, 4),
('COMM', '交通银行', 10.00, 50000.00, 0.001, 5),
('CMB', '招商银行', 10.00, 50000.00, 0.001, 6),
('CITIC', '中信银行', 10.00, 50000.00, 0.001, 7),
('CEB', '光大银行', 10.00, 50000.00, 0.001, 8),
('CMBC', '中国民生银行', 10.00, 50000.00, 0.001, 9),
('PAB', '平安银行', 10.00, 50000.00, 0.001, 10)
ON DUPLICATE KEY UPDATE 
  `bank_name` = VALUES(`bank_name`),
  `min_amount` = VALUES(`min_amount`),
  `max_amount` = VALUES(`max_amount`),
  `updated_at` = CURRENT_TIMESTAMP;

-- 8. 为测试用户插入微信账户
INSERT INTO `wechat_account` (`user_id`, `openid`, `nickname`, `real_name`, `is_verified`, `is_default`) VALUES
(1, 'oW20C7mVlW8e3W2AgUGtDTJeAbQU', '润生', '张润生', 1, 1)
ON DUPLICATE KEY UPDATE 
  `nickname` = VALUES(`nickname`),
  `real_name` = VALUES(`real_name`),
  `updated_at` = CURRENT_TIMESTAMP;

-- 9. 为测试用户插入银行卡（更新现有记录）
UPDATE `bank_card` SET 
  `bank_code` = 'CMB',
  `card_number_mask` = '**** **** **** 6789',
  `card_type` = 1,
  `phone` = '***********',
  `is_verified` = 1,
  `status` = 1
WHERE `user_id` = 1 AND `bank_name` = '招商银行';

-- 如果不存在则插入
INSERT IGNORE INTO `bank_card` (`user_id`, `bank_name`, `bank_code`, `card_number`, `card_number_mask`, `card_holder`, `card_type`, `phone`, `is_verified`, `status`) VALUES
(1, '招商银行', 'CMB', 'encrypted_6217000123456789', '**** **** **** 6789', '张润生', 1, '***********', 1, 1);

-- 10. 插入测试提现记录
INSERT INTO `withdraw` (`user_id`, `withdraw_no`, `amount`, `fee`, `actual_amount`, `withdraw_type`, `account_id`, `account_info`, `status`, `apply_time`, `complete_time`, `remark`) VALUES
(1, 'WD202501290001', 100.00, 0.60, 99.40, 'wechat', 1, '{"type":"wechat","nickname":"润生","real_name":"张润生"}', 4, '2025-01-29 10:00:00', '2025-01-29 11:30:00', '测试微信提现'),
(1, 'WD202501290002', 200.00, 2.00, 198.00, 'bank_card', 1, '{"type":"bank_card","bank_name":"招商银行","card_mask":"**** **** **** 6789"}', 4, '2025-01-29 14:00:00', '2025-01-29 16:00:00', '测试银行卡提现'),
(1, 'WD202501290003', 50.00, 0.30, 49.70, 'wechat', 1, '{"type":"wechat","nickname":"润生","real_name":"张润生"}', 0, '2025-01-29 18:00:00', NULL, '待审核提现申请')
ON DUPLICATE KEY UPDATE `updated_at` = CURRENT_TIMESTAMP;

-- 显示创建结果
SELECT '✅ 提现功能数据库完善完成' as message;

-- 验证表结构
SELECT 
  table_name as '表名',
  table_comment as '表说明'
FROM information_schema.tables 
WHERE table_schema = 'mall' 
AND (table_name LIKE '%withdraw%' OR table_name LIKE '%bank%' OR table_name LIKE '%wechat%')
ORDER BY table_name;
