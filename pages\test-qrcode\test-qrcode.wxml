<!--pages/test-qrcode/test-qrcode.wxml-->
<view class="container">
  <view class="header">
    <text class="title">二维码测试页面</text>
    <text class="subtitle">测试修复后的二维码组件</text>
  </view>

  <view class="qrcode-section">
    <view class="qrcode-title">WiFi二维码测试</view>
    <view class="wifi-info">
      <text>WiFi名称: {{testWifi.ssid}}</text>
      <text>WiFi密码: {{testWifi.password}}</text>
    </view>
    
    <view class="qrcode-wrapper">
      <qrcode
        ssid="{{testWifi.ssid}}"
        password="{{testWifi.password}}"
        size="500"
        bind:generated="onQrCodeGenerated"
        bind:tap="onQrCodeTap"
        bind:longpress="onQrCodeLongPress"
      />
    </view>
  </view>

  <view class="instructions">
    <text class="instruction-title">测试说明:</text>
    <text>1. 查看控制台日志输出</text>
    <text>2. 检查二维码是否正常显示</text>
    <text>3. 用手机扫描二维码测试连接</text>
    <text>4. 如果显示备用信息也是正常的</text>
  </view>
</view>
