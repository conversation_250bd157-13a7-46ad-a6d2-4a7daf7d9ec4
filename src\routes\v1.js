/**
 * API v1 版本路由配置
 */

const express = require('express');
const router = express.Router();

// 引入各个模块的路由
const withdrawRouter = require('./withdraw');
const incomeRouter = require('./income');
const teamRouter = require('./team');
const cartRouter = require('./cart');
const authRouter = require('./auth');
const adsRouter = require('./ads');
const userRouter = require('./user');
const orderRouter = require('./order');
const paymentRouter = require('./payment');
const allianceRouter = require('./alliance');
const wifiRouter = require('./wifi');
const goodsController = require('../controllers/goods');

// 配置提现相关路由
router.use('/client/withdraw', withdrawRouter);

// 配置收入相关路由
router.use('/client/income', incomeRouter);
console.log('✅ 已注册收入路由: /api/v1/client/income');

// 配置团队相关路由
router.use('/client/team', teamRouter);
console.log('✅ 已注册团队路由: /api/v1/client/team');

// 配置购物车相关路由
router.use('/client/cart', cartRouter);
console.log('✅ 已注册购物车路由: /api/v1/client/cart');

// 配置认证相关路由
router.use('/client/auth', authRouter);
console.log('✅ 已注册认证路由: /api/v1/client/auth');

// 配置广告相关路由
router.use('/client/ads', adsRouter);
console.log('✅ 已注册广告路由: /api/v1/client/ads');

// 配置用户相关路由
router.use('/client/user', userRouter);
console.log('✅ 已注册用户路由: /api/v1/client/user');

// 配置地址相关路由别名（兼容前端配置）
// 将 /client/address/* 请求转发到 /client/user/address/*
router.use('/client/address', (req, res, next) => {
  // 修改请求路径，添加 /address 前缀
  req.url = '/address' + req.url;
  userRouter(req, res, next);
});
console.log('✅ 已注册地址路由别名: /api/v1/client/address -> /api/v1/client/user/address');

// 配置订单相关路由
router.use('/client/order', orderRouter);
console.log('✅ 已注册订单路由: /api/v1/client/order');

// 配置支付相关路由
router.use('/client/payment', paymentRouter);
console.log('✅ 已注册支付路由: /api/v1/client/payment');

// 配置联盟相关路由
router.use('/client/alliance', allianceRouter);
console.log('✅ 已注册联盟路由: /api/v1/client/alliance');

// 配置WiFi相关路由
router.use('/client/wifi', wifiRouter);
console.log('✅ 已注册WiFi路由: /api/v1/client/wifi');

// 配置商品相关路由
router.get('/client/goods/list', (req, res) => {
  console.log('直接处理客户端商品列表请求，无需认证');
  goodsController.getGoodsList(req, res);
});

router.get('/client/goods/detail/:id', (req, res) => {
  console.log('直接处理客户端商品详情请求，无需认证');
  goodsController.getGoodsDetail(req, res);
});

router.get('/client/goods/categories', (req, res) => {
  console.log('直接处理客户端商品分类请求，无需认证');
  goodsController.getCategories(req, res);
});

// 推荐商品接口
router.get('/client/goods/recommend', (req, res) => {
  console.log('直接处理客户端推荐商品请求，无需认证');
  // 从查询参数获取limit
  const { limit = 3 } = req.query;

  // 修改req.query来获取推荐商品
  req.query.isRecommend = true;
  req.query.limit = limit;

  goodsController.getGoodsList(req, res);
});

console.log('✅ 已注册商品路由: /api/v1/client/goods');

// WiFi二维码专用路由 - 使用完全不同的路径避免冲突
router.get('/client/wifi-qrcode', async (req, res) => {
  console.log('处理独立WiFi二维码生成请求，参数:', req.query);
  try {
    const { ssid, password, encryption = 'WPA', hidden = 'false', adEnabled = 'false' } = req.query;

    if (!ssid || !password) {
      return res.status(400).json({
        status: 'error',
        message: 'SSID和密码不能为空'
      });
    }

    // 构建WiFi连接字符串
    // 格式: WIFI:T:<加密类型>;S:<SSID>;P:<密码>;H:<是否隐藏>;;
    const wifiString = `WIFI:T:${encryption};S:${ssid};P:${password};H:${hidden};;`;

    // 使用第三方服务生成二维码
    // 这里使用QR Server API作为示例
    const qrServerUrl = `https://api.qrserver.com/v1/create-qr-code/?data=${encodeURIComponent(wifiString)}&size=300x300&format=png`;

    console.log('生成WiFi二维码成功，URL:', qrServerUrl);

    // 返回二维码URL
    return res.json({
      status: 'success',
      message: '二维码生成成功',
      data: {
        qrcode_url: qrServerUrl,
        wifi_string: wifiString,
        ad_enabled: adEnabled === 'true'
      }
    });
  } catch (err) {
    console.error('生成WiFi二维码失败:', err);
    return res.status(500).json({
      status: 'error',
      message: '生成WiFi二维码失败: ' + err.message
    });
  }
});

console.log('✅ 已注册WiFi二维码路由: /api/v1/client/wifi-qrcode');

// 健康检查接口
router.get('/health', (req, res) => {
  res.json({
    success: true,
    message: 'API v1 服务正常',
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  });
});

// 404处理
router.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: '未找到请求的资源',
    path: req.originalUrl,
    method: req.method
  });
});

module.exports = router;