/**
 * weapp-qrcode
 * 微信小程序生成二维码工具类
 */

const QRCode = (function() {
  // 二维码容错级别
  const QRErrorCorrectLevel = {
    L: 1,  // 可纠正约 7% 的错误
    M: 0,  // 可纠正约 15% 的错误
    Q: 3,  // 可纠正约 25% 的错误
    H: 2   // 可纠正约 30% 的错误
  };
  
  // 二维码掩码模式
  const QRMaskPattern = {
    PATTERN000: 0,
    PATTERN001: 1,
    PATTERN010: 2,
    PATTERN011: 3,
    PATTERN100: 4,
    PATTERN101: 5,
    PATTERN110: 6,
    PATTERN111: 7
  };

  // 二维码编码模式
  const QRMode = {
    MODE_NUMBER: 1 << 0,
    MODE_ALPHA_NUM: 1 << 1,
    MODE_8BIT_BYTE: 1 << 2,
    MODE_KANJI: 1 << 3
  };

  // 辅助函数
  function _getUTF8Length(str) {
    let len = str.length;
    for (let i = 0; i < str.length; i++) {
      const code = str.charCodeAt(i);
      if (code >= 0x0800 && code <= 0xffff) {
        len += 1;
      } else if (code >= 0x10000) {
        len += 2;
      }
    }
    return len;
  }

  /**
   * QR8BitByte
   * @param {String} data 
   */
  function QR8BitByte(data) {
    this.mode = QRMode.MODE_8BIT_BYTE;
    this.data = data;
  }

  QR8BitByte.prototype = {
    getLength: function() {
      return _getUTF8Length(this.data);
    },
    
    write: function(buffer) {
      for (let i = 0; i < this.data.length; i++) {
        const code = this.data.charCodeAt(i);
        if (code >= 0x0800 && code <= 0xffff) {
          buffer.put(((code >> 12) & 0x0F) | 0xE0, 8);
          buffer.put(((code >> 6) & 0x3F) | 0x80, 8);
          buffer.put((code & 0x3F) | 0x80, 8);
        } else if (code >= 0x10000) {
          buffer.put(((code >> 18) & 0x07) | 0xF0, 8);
          buffer.put(((code >> 12) & 0x3F) | 0x80, 8);
          buffer.put(((code >> 6) & 0x3F) | 0x80, 8);
          buffer.put((code & 0x3F) | 0x80, 8);
        } else if (code >= 0x0080) {
          buffer.put(((code >> 6) & 0x1F) | 0xC0, 8);
          buffer.put((code & 0x3F) | 0x80, 8);
        } else {
          buffer.put(code, 8);
        }
      }
    }
  };

  /**
   * QRBitBuffer
   */
  function QRBitBuffer() {
    this.buffer = [];
    this.length = 0;
  }

  QRBitBuffer.prototype = {
    get: function(index) {
      const bufIndex = Math.floor(index / 8);
      return ((this.buffer[bufIndex] >>> (7 - index % 8)) & 1) == 1;
    },
    
    put: function(num, length) {
      for (let i = 0; i < length; i++) {
        this.putBit(((num >>> (length - i - 1)) & 1) == 1);
      }
    },
    
    getLengthInBits: function() {
      return this.length;
    },
    
    putBit: function(bit) {
      const bufIndex = Math.floor(this.length / 8);
      if (this.buffer.length <= bufIndex) {
        this.buffer.push(0);
      }
      
      if (bit) {
        this.buffer[bufIndex] |= (0x80 >>> (this.length % 8));
      }
      
      this.length++;
    }
  };

  /**
   * QRPolynomial
   * @param {Array} num   
   * @param {Number} shift 
   */
  function QRPolynomial(num, shift) {
    if (num.length === undefined) {
      throw new Error(num.length + "/" + shift);
    }

    let offset = 0;
    while (offset < num.length && num[offset] === 0) {
      offset++;
    }

    this.num = new Array(num.length - offset + shift);
    for (let i = 0; i < num.length - offset; i++) {
      this.num[i] = num[i + offset];
    }
  }

  QRPolynomial.prototype = {
    get: function(index) {
      return this.num[index];
    },
    
    getLength: function() {
      return this.num.length;
    },
    
    multiply: function(e) {
      const num = new Array(this.getLength() + e.getLength() - 1);

      for (let i = 0; i < this.getLength(); i++) {
        for (let j = 0; j < e.getLength(); j++) {
          num[i + j] ^= QRMath.gexp(QRMath.glog(this.get(i)) + QRMath.glog(e.get(j)));
        }
      }

      return new QRPolynomial(num, 0);
    },
    
    mod: function(e) {
      if (this.getLength() - e.getLength() < 0) {
        return this;
      }

      const ratio = QRMath.glog(this.get(0)) - QRMath.glog(e.get(0));
      const num = new Array(this.getLength());
      
      for (let i = 0; i < this.getLength(); i++) {
        num[i] = this.get(i);
      }
      
      for (let i = 0; i < e.getLength(); i++) {
        num[i] ^= QRMath.gexp(QRMath.glog(e.get(i)) + ratio);
      }

      return new QRPolynomial(num, 0).mod(e);
    }
  };

  /**
   * QRRSBlock
   * @param {Number} totalCount 
   * @param {Number} dataCount  
   */
  function QRRSBlock(totalCount, dataCount) {
    this.totalCount = totalCount;
    this.dataCount = dataCount;
  }

  /**
   * QRMath
   */
  const QRMath = {
    glog: function(n) {
      if (n < 1) {
        throw new Error("glog(" + n + ")");
      }
      return QRMath.LOG_TABLE[n];
    },

    gexp: function(n) {
      while (n < 0) {
        n += 255;
      }
      while (n >= 256) {
        n -= 255;
      }
      return QRMath.EXP_TABLE[n];
    },

    EXP_TABLE: new Array(256),
    LOG_TABLE: new Array(256)
  };

  // 初始化数学表
  for (let i = 0; i < 8; i++) {
    QRMath.EXP_TABLE[i] = 1 << i;
  }
  for (let i = 8; i < 256; i++) {
    QRMath.EXP_TABLE[i] = QRMath.EXP_TABLE[i - 4] ^ QRMath.EXP_TABLE[i - 5] ^ QRMath.EXP_TABLE[i - 6] ^ QRMath.EXP_TABLE[i - 8];
  }
  for (let i = 0; i < 255; i++) {
    QRMath.LOG_TABLE[QRMath.EXP_TABLE[i]] = i;
  }

  /**
   * QRCode主类
   * @param {String} canvasId 
   * @param {Object} options  
   */
  function QRCode(canvasId, options) {
    // 默认参数
    this.options = {
      width: 256,
      height: 256,
      typeNumber: -1,
      correctLevel: QRErrorCorrectLevel.H,
      background: "#ffffff",
      foreground: "#000000",
      text: "",
      image: null
    };

    // 合并参数
    if (typeof options === 'object') {
      for (const key in options) {
        this.options[key] = options[key];
      }
    }

    this.canvasId = canvasId;
    this._oContext = null;
    this._htOption = this.options;
    this._elImage = this._htOption.image; // Logo图片

    // 如果有指定text，直接生成
    if (this._htOption.text) {
      this.makeCode(this._htOption.text);
    }
  }

  QRCode.prototype.makeCode = function(sText) {
    this._htOption.text = sText;
    const oQRCode = new QRCodeModel(this._getTypeNumber(sText, this._htOption.correctLevel), this._htOption.correctLevel);
    oQRCode.addData(sText);
    oQRCode.make();
    
    const ctx = wx.createCanvasContext(this.canvasId);
    const nCount = oQRCode.getModuleCount();
    const nWidth = this._htOption.width / nCount;
    const nHeight = this._htOption.height / nCount;
    
    // 清空画布
    ctx.setFillStyle(this._htOption.background);
    ctx.fillRect(0, 0, this._htOption.width, this._htOption.height);

    // 绘制二维码
    for (let row = 0; row < nCount; row++) {
      for (let col = 0; col < nCount; col++) {
        const bIsDark = oQRCode.isDark(row, col);
        const nLeft = col * nWidth;
        const nTop = row * nHeight;
        ctx.setFillStyle(bIsDark ? this._htOption.foreground : this._htOption.background);
        ctx.fillRect(nLeft, nTop, nWidth, nHeight);
      }
    }

    // 绘制Logo
    if (this._elImage && this._elImage.imageResource) {
      const imgW = this._elImage.dWidth || 40;
      const imgH = this._elImage.dHeight || 40;
      const imgX = this._elImage.dx !== undefined ? this._elImage.dx : (this._htOption.width - imgW) / 2;
      const imgY = this._elImage.dy !== undefined ? this._elImage.dy : (this._htOption.height - imgH) / 2;
      
      // 加载Logo图片
      try {
        ctx.drawImage(this._elImage.imageResource, imgX, imgY, imgW, imgH);
      } catch (e) {
        console.error('绘制Logo失败', e);
      }
    }
    
    ctx.draw();
  };

  QRCode.prototype._getTypeNumber = function(sText, nCorrectLevel) {
    let nType = 1;
    const length = _getUTF8Length(sText);

    for (let i = 0, len = QRCodeLimitLength.length; i < len; i++) {
      const nLimit = 0;
      
      switch (nCorrectLevel) {
        case QRErrorCorrectLevel.L:
          nLimit = QRCodeLimitLength[i][0];
          break;
        case QRErrorCorrectLevel.M:
          nLimit = QRCodeLimitLength[i][1];
          break;
        case QRErrorCorrectLevel.Q:
          nLimit = QRCodeLimitLength[i][2];
          break;
        case QRErrorCorrectLevel.H:
          nLimit = QRCodeLimitLength[i][3];
          break;
      }
      
      if (length <= nLimit) {
        break;
      } else {
        nType++;
      }
    }
    
    if (nType > QRCodeLimitLength.length) {
      throw new Error("Too long data");
    }
    
    return nType;
  };

  // QR码数据长度限制
  const QRCodeLimitLength = [
    [17, 14, 11, 7],
    [32, 26, 20, 14],
    [53, 42, 32, 24],
    [78, 62, 46, 34],
    [106, 84, 60, 44],
    [134, 106, 74, 58],
    [154, 122, 86, 64],
    [192, 152, 108, 84],
    [230, 180, 130, 98],
    [271, 213, 151, 119],
    [321, 251, 177, 137],
    [367, 287, 203, 155],
    [425, 331, 241, 177],
    [458, 362, 258, 194],
    [520, 412, 292, 220],
    [586, 450, 322, 250],
    [644, 504, 364, 280],
    [718, 560, 394, 310],
    [792, 624, 442, 338],
    [858, 666, 482, 382],
    [929, 711, 509, 403],
    [1003, 779, 565, 439],
    [1091, 857, 611, 461],
    [1171, 911, 661, 511],
    [1273, 997, 715, 535],
    [1367, 1059, 751, 593],
    [1465, 1125, 805, 625],
    [1528, 1190, 868, 658],
    [1628, 1264, 908, 698],
    [1732, 1370, 982, 742],
    [1840, 1452, 1030, 790],
    [1952, 1538, 1112, 842],
    [2068, 1628, 1168, 898],
    [2188, 1722, 1228, 958],
    [2303, 1809, 1283, 983],
    [2431, 1911, 1351, 1051],
    [2563, 1989, 1423, 1093],
    [2699, 2099, 1499, 1139],
    [2809, 2213, 1579, 1219],
    [2953, 2331, 1663, 1273]
  ];

  /**
   * QRCodeModel
   * @param {Number} typeNumber
   * @param {Number} errorCorrectLevel
   */
  function QRCodeModel(typeNumber, errorCorrectLevel) {
    this.typeNumber = typeNumber;
    this.errorCorrectLevel = errorCorrectLevel;
    this.modules = null;
    this.moduleCount = 0;
    this.dataCache = null;
    this.dataList = [];
  }

  QRCodeModel.prototype = {
    addData: function(data) {
      const newData = new QR8BitByte(data);
      this.dataList.push(newData);
      this.dataCache = null;
    },
    
    isDark: function(row, col) {
      if (row < 0 || this.moduleCount <= row || col < 0 || this.moduleCount <= col) {
        throw new Error(row + "," + col);
      }
      return this.modules[row][col];
    },
    
    getModuleCount: function() {
      return this.moduleCount;
    },
    
    make: function() {
      if (this.typeNumber < 1) {
        let typeNumber = 1;
        for (typeNumber = 1; typeNumber < 40; typeNumber++) {
          const rsBlocks = QRRSBlock.getRSBlocks(typeNumber, this.errorCorrectLevel);
          const buffer = new QRBitBuffer();
          
          let totalDataCount = 0;
          for (let i = 0; i < rsBlocks.length; i++) {
            totalDataCount += rsBlocks[i].dataCount;
          }
          
          for (let i = 0; i < this.dataList.length; i++) {
            const data = this.dataList[i];
            buffer.put(data.mode, 4);
            buffer.put(data.getLength(), QRUtil.getLengthInBits(data.mode, typeNumber));
            data.write(buffer);
          }
          
          if (buffer.getLengthInBits() <= totalDataCount * 8) {
            break;
          }
        }
        
        this.typeNumber = typeNumber;
      }
      
      this._make();
    },
    
    _make: function() {
      const rsBlocks = QRRSBlock.getRSBlocks(this.typeNumber, this.errorCorrectLevel);
      const buffer = new QRBitBuffer();
      
      // 添加数据
      for (let i = 0; i < this.dataList.length; i++) {
        const data = this.dataList[i];
        buffer.put(data.mode, 4);
        buffer.put(data.getLength(), QRUtil.getLengthInBits(data.mode, this.typeNumber));
        data.write(buffer);
      }
      
      // 计算总字节数
      let totalDataCount = 0;
      for (let i = 0; i < rsBlocks.length; i++) {
        totalDataCount += rsBlocks[i].dataCount;
      }
      
      if (buffer.getLengthInBits() > totalDataCount * 8) {
        throw new Error("code length overflow. (" + buffer.getLengthInBits() + ">" + totalDataCount * 8 + ")");
      }
      
      // 补充剩余空间
      if (buffer.getLengthInBits() + 4 <= totalDataCount * 8) {
        buffer.put(0, 4);
      }
      
      // 对齐到8位
      while (buffer.getLengthInBits() % 8 != 0) {
        buffer.putBit(false);
      }
      
      // 填充剩余字节
      let maxDcCount = 0;
      let maxEcCount = 0;
      let dcdata = new Array(rsBlocks.length);
      let ecdata = new Array(rsBlocks.length);
      
      for (let r = 0; r < rsBlocks.length; r++) {
        const dcCount = rsBlocks[r].dataCount;
        const ecCount = rsBlocks[r].totalCount - dcCount;
        
        maxDcCount = Math.max(maxDcCount, dcCount);
        maxEcCount = Math.max(maxEcCount, ecCount);
        
        dcdata[r] = new Array(dcCount);
        for (let i = 0; i < dcdata[r].length; i++) {
          dcdata[r][i] = 0xff & buffer.buffer[i + offset];
        }
        
        const offset = 0;
        
        const rsPoly = QRUtil.getErrorCorrectPolynomial(ecCount);
        const rawPoly = new QRPolynomial(dcdata[r], rsPoly.getLength() - 1);
        const modPoly = rawPoly.mod(rsPoly);
        
        ecdata[r] = new Array(rsPoly.getLength() - 1);
        for (let i = 0; i < ecdata[r].length; i++) {
          const modIndex = i + modPoly.getLength() - ecdata[r].length;
          ecdata[r][i] = (modIndex >= 0) ? modPoly.get(modIndex) : 0;
        }
      }
      
      // 初始化模块
      this.moduleCount = this.typeNumber * 4 + 17;
      this.modules = new Array(this.moduleCount);
      for (let i = 0; i < this.moduleCount; i++) {
        this.modules[i] = new Array(this.moduleCount);
        for (let j = 0; j < this.moduleCount; j++) {
          this.modules[i][j] = null;
        }
      }
      
      // 设置位置探测图形
      this.setupPositionProbePattern(0, 0);
      this.setupPositionProbePattern(this.moduleCount - 7, 0);
      this.setupPositionProbePattern(0, this.moduleCount - 7);
    },
    
    setupPositionProbePattern: function(row, col) {
      for (let r = -1; r <= 7; r++) {
        if (row + r <= -1 || this.moduleCount <= row + r) continue;
        
        for (let c = -1; c <= 7; c++) {
          if (col + c <= -1 || this.moduleCount <= col + c) continue;
          
          if ((0 <= r && r <= 6 && (c == 0 || c == 6)) ||
              (0 <= c && c <= 6 && (r == 0 || r == 6)) ||
              (2 <= r && r <= 4 && 2 <= c && c <= 4)) {
            this.modules[row + r][col + c] = true;
          } else {
            this.modules[row + r][col + c] = false;
          }
        }
      }
    }
  };

  // QRCode辅助工具
  const QRUtil = {
    /**
     * 获取模式字段长度
     */
    getLengthInBits: function(mode, type) {
      if (1 <= type && type < 10) {
        switch (mode) {
          case QRMode.MODE_NUMBER: return 10;
          case QRMode.MODE_ALPHA_NUM: return 9;
          case QRMode.MODE_8BIT_BYTE: return 8;
          case QRMode.MODE_KANJI: return 8;
          default: throw new Error("mode:" + mode);
        }
      } else if (type < 27) {
        switch (mode) {
          case QRMode.MODE_NUMBER: return 12;
          case QRMode.MODE_ALPHA_NUM: return 11;
          case QRMode.MODE_8BIT_BYTE: return 16;
          case QRMode.MODE_KANJI: return 10;
          default: throw new Error("mode:" + mode);
        }
      } else if (type < 41) {
        switch (mode) {
          case QRMode.MODE_NUMBER: return 14;
          case QRMode.MODE_ALPHA_NUM: return 13;
          case QRMode.MODE_8BIT_BYTE: return 16;
          case QRMode.MODE_KANJI: return 12;
          default: throw new Error("mode:" + mode);
        }
      } else {
        throw new Error("type:" + type);
      }
    },
    
    /**
     * 获取错误校正多项式
     */
    getErrorCorrectPolynomial: function(errorCorrectLength) {
      let a = new QRPolynomial([1], 0);
      
      for (let i = 0; i < errorCorrectLength; i++) {
        a = a.multiply(new QRPolynomial([1, QRMath.gexp(i)], 0));
      }
      
      return a;
    },
    
    /**
     * 获取掩码图案
     */
    getMask: function(maskPattern, i, j) {
      switch (maskPattern) {
        case QRMaskPattern.PATTERN000: return (i + j) % 2 == 0;
        case QRMaskPattern.PATTERN001: return i % 2 == 0;
        case QRMaskPattern.PATTERN010: return j % 3 == 0;
        case QRMaskPattern.PATTERN011: return (i + j) % 3 == 0;
        case QRMaskPattern.PATTERN100: return (Math.floor(i / 2) + Math.floor(j / 3)) % 2 == 0;
        case QRMaskPattern.PATTERN101: return (i * j) % 2 + (i * j) % 3 == 0;
        case QRMaskPattern.PATTERN110: return ((i * j) % 2 + (i * j) % 3) % 2 == 0;
        case QRMaskPattern.PATTERN111: return ((i * j) % 3 + (i + j) % 2) % 2 == 0;
        default: throw new Error("maskPattern:" + maskPattern);
      }
    }
  };

  /**
   * 创建基本QR对象
   */
  const _createQRCode = function(canvasId, options) {
    options = options || {};
    return new QRCode(canvasId, options);
  };

  // 将 CorrectLevel 添加到导出函数上
  _createQRCode.CorrectLevel = QRErrorCorrectLevel;

  return _createQRCode;
})();

module.exports = QRCode; 